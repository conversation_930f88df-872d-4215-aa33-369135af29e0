// Stub for Material UI utils that are missing in v7
// This provides basic utility functions

// Import functions from colorManipulator to avoid duplication
import { capitalize, ownerWindow } from './colorManipulator.js';

// Stub implementations for Material UI utils
export const createSvgIcon = () => null;
export const mergeSlotProps = (props) => props;
export const unstable_ClassNameGenerator = {};
export const unstable_memoTheme = (theme) => theme;
export const unstable_useEnhancedEffect = () => {};
export const unstable_useId = () => 'stub-id';
export const useControlled = () => [null, () => {}];
export const useEventCallback = (fn) => fn;
export const useForkRef = () => null;

// Re-export functions from colorManipulator
export { capitalize, ownerWindow };
