// Stub for Material UI utils that are missing in v7
// This provides basic utility functions

export const capitalize = (str) => {
  // Simple capitalize function
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const ownerWindow = (node) => {
  // Simple owner window function
  return (node && node.ownerDocument && node.ownerDocument.defaultView) || window;
};

// Stub implementations for Material UI utils
export const createSvgIcon = () => null;
export const mergeSlotProps = (props) => props;
export const unstable_ClassNameGenerator = {};
export const unstable_memoTheme = (theme) => theme;
export const unstable_useEnhancedEffect = () => {};
export const unstable_useId = () => 'stub-id';
export const useControlled = () => [null, () => {}];
export const useEventCallback = (fn) => fn;
export const useForkRef = () => null;
