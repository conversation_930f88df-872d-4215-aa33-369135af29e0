// Stub for Material UI DefaultPropsProvider that's missing in v7
// This provides a simple passthrough component to avoid build errors

import React from 'react';

// Simple passthrough component that just renders children
const DefaultPropsProvider = ({ children }) => {
  return children;
};

// Simple hook that returns props as-is (no default props processing)
const useDefaultProps = ({ props, name }) => {
  return props;
};

export default DefaultPropsProvider;
export { DefaultPropsProvider, useDefaultProps };
