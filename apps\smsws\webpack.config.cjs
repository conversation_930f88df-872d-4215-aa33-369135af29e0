const { ModuleFederationPlugin } = require('@module-federation/enhanced');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const path = require('path');

module.exports = {
  mode: 'development',
  entry: './src/main.tsx',
  devtool: 'eval-source-map',

  output: {
    publicPath: 'auto'
    // publicPath: 'http://localhost:3001/',
  },

  resolve: {
    extensions: ['.tsx', '.ts', '.jsx', '.js'],
    alias: {
      '@pnmui/common': path.resolve(__dirname, '../../packages/common/src'),
      '@pnmui/common/*': path.resolve(__dirname, '../../packages/common/src/*'),
      // Fix Material UI DefaultPropsProvider issue
      '@mui/system/DefaultPropsProvider': path.resolve(__dirname, 'src/stubs/DefaultPropsProvider.js'),
      '@mui/material/DefaultPropsProvider': path.resolve(__dirname, 'src/stubs/DefaultPropsProvider.js'),
      // Fix Material UI colorManipulator issue
      '@mui/system/colorManipulator': path.resolve(__dirname, 'src/stubs/colorManipulator.js'),
      // Fix Material UI utils issue
      '@mui/material/utils': path.resolve(__dirname, 'src/stubs/utils.js')
    }
  },

  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: {
          loader: 'ts-loader',
          options: {
            configFile: 'tsconfig.webpack.json',
            transpileOnly: true, // Skip type checking for faster builds
            compilerOptions: {
              noEmit: false,
              isolatedModules: true
            }
          }
        },
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/,
        type: 'asset/resource',
      },
    ],
  },

  plugins: [
    new ModuleFederationPlugin({
        name: 'smsws',
        filename: 'remoteEntry.js',
        dts: false, // Disable DTS generation to avoid the error
        exposes: {
          // Expose micro frontend components
          './MicroFrontend': './src/micro-frontend.tsx',
          // Expose the full app with layout for standalone mode
          './App': './src/App.tsx',
        },
        shared: {
          react: {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: true,
          },
          'react-dom': {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: true,
          },
          'react-router-dom': {
            singleton: true,
            requiredVersion: '^6.0.0',
            eager: false,
          },
          '@mui/material': {
            singleton: true,
            requiredVersion: '7.0.2',
            version: '7.0.2',
            eager: false,
          },
          '@mui/icons-material': {
            singleton: true,
            requiredVersion: '^7.0.0',
            eager: false,
          },
          '@mui/system': {
            singleton: true,
            requiredVersion: '^7.0.1',
            eager: false,
          },
          '@mui/styled-engine': {
            singleton: true,
            requiredVersion: '^7.0.1',
            eager: false,
          },
        },
    }),

    // Ignore problematic KendoReact layout and progressbar interface modules
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/stepper\/interfaces\/|^\.\/appbar\/interfaces\/|^\.\/tilelayout\/interfaces\/|^\.\/bottomnavigation\/|^\.\/stacklayout\/|^\.\/gridlayout\/interfaces\/|^\.\/actionsheet\/interfaces\/|^\.\/chunkprogressbar\/interfaces\/|^\.\/progressbar\/interfaces\/|interfaces\/main$|models\/events$/,
      contextRegExp: /@progress\/kendo-react-layout|@progress\/kendo-react-progressbars|@progress\/kendo-react-dateinputs/
    }),

    // Additional ignore plugin for specific missing interface files
    new webpack.IgnorePlugin({
      resourceRegExp: /StepProps|StepChangeEvent|StepFocusEvent|AppBarProps|AppBarSectionProps|AppBarSpacerProps|BottomNavigationProps|BottomNavigationItemProps|StackLayoutProps|GridLayoutProps|GridLayoutItemProps|GridLayoutRowProps|GridLayoutColumnProps|ActionSheetItemProps|ChunkProgressBarProps|ProgressBarProps|ProgressBarAnimation|LabelProps|BottomNavigationProps|BottomNavigationItemProps/,
      contextRegExp: /@progress\/kendo-react-layout|@progress\/kendo-react-progressbars|@progress\/kendo-react-dateinputs/
    }),

    // Plugin to provide fallback for missing DefaultPropsProvider
    new webpack.NormalModuleReplacementPlugin(
      /\.\.\/DefaultPropsProvider(\/index\.js)?$/,
      path.resolve(__dirname, 'src/stubs/DefaultPropsProvider.js')
    ),

    new HtmlWebpackPlugin({
      template: './index.html',
      title: 'SMSWS Micro Frontend',
    }),
  ],

  devServer: {
    port: 3001,
    hot: true,
    historyApiFallback: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
    },
  },
};
