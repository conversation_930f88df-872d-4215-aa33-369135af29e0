{"name": "smsws", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "dev:mf": "webpack serve --mode development --config webpack.config.cjs", "build:mf": "webpack --mode production --config webpack.config.cjs", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:report": "playwright show-report"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^7.0.1", "@mui/material": "7.0.2", "@mui/styled-engine": "^7.0.1", "@mui/system": "^7.0.1", "@pnmui/common": "workspace:*", "@progress/kendo-data-query": "^1.6.0", "@progress/kendo-drawing": "^1.17.5", "@progress/kendo-licensing": "^1.3.1", "@progress/kendo-react-animation": "^5.16.1", "@progress/kendo-react-buttons": "^5.16.1", "@progress/kendo-react-data-tools": "^5.16.1", "@progress/kendo-react-dateinputs": "^5.16.1", "@progress/kendo-react-dropdowns": "^5.16.1", "@progress/kendo-react-form": "^5.16.1", "@progress/kendo-react-grid": "^5.16.1", "@progress/kendo-react-inputs": "^5.16.1", "@progress/kendo-react-intl": "^5.16.1", "@progress/kendo-react-layout": "^5.16.1", "@progress/kendo-react-popup": "^5.16.1", "@progress/kendo-react-progressbars": "^5.16.1", "@progress/kendo-svg-icons": "^2.0.0", "@progress/kendo-theme-default": "^6.7.0", "@progress/kendo-theme-material": "^6.7.0", "dayjs": "^1.11.10", "material-ui-confirm": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-material-ui-form-validator": "^4.0.2", "react-router-dom": "^7.5.0", "react-toastify": "^11.0.5"}, "devDependencies": {"@module-federation/enhanced": "^0.14.3", "@playwright/test": "^1.52.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "buffer": "^6.0.3", "css-loader": "^7.1.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "html-webpack-plugin": "^5.6.3", "http-proxy-middleware": "^3.0.3", "https-browserify": "^1.0.0", "path-browserify": "^1.0.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.3.3", "url": "^0.11.4", "util": "^0.12.5", "vite": "^5.0.8", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}